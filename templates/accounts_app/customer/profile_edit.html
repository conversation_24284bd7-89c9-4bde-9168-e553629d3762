{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Edit Profile - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Profile Edit */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Profile Edit Section */
    .profile-edit-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .profile-edit-container {
        max-width: 1000px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .profile-edit-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Header Section */
    .profile-edit-header {
        background: var(--cw-gradient-card-subtle);
        padding: 3rem 3rem 2rem;
        text-align: center;
        position: relative;
        border-bottom: 1px solid var(--cw-brand-accent);
    }

    .profile-edit-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="edit-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23f1d4c4" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23edit-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .profile-edit-header .content {
        position: relative;
        z-index: 2;
    }

    .edit-icon {
        width: 80px;
        height: 80px;
        background: var(--cw-gradient-brand-button);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .edit-title {
        font-family: var(--cw-font-display);
        font-size: 2.75rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .edit-subtitle {
        font-size: 1.25rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .profile-edit-body {
        padding: 3rem;
    }

    /* Section Headers */
    .section-header {
        font-family: var(--cw-font-heading);
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid var(--cw-brand-accent);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-header i {
        color: var(--cw-brand-primary);
        font-size: 1.25rem;
    }

    /* Form Styling */
    .form-label {
        color: var(--cw-neutral-700);
        font-weight: 600;
        font-family: var(--cw-font-heading);
        margin-bottom: 0.5rem;
    }

    .form-control-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
        color: var(--cw-neutral-800);
        font-family: var(--cw-font-primary);
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-control-cw.is-invalid {
        border-color: #dc2626;
        box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.1);
    }

    .form-select-cw {
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        background: white;
        color: var(--cw-neutral-800);
        font-family: var(--cw-font-primary);
    }

    .form-select-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    /* Profile Picture Preview */
    .profile-picture-preview-container {
        position: relative;
        display: inline-block;
        margin-bottom: 1rem;
        padding: 1rem;
        background: white;
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        border: 1px solid var(--cw-brand-accent);
        transition: all 0.3s ease;
    }

    .profile-picture-preview-container:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
        border-color: var(--cw-brand-primary);
    }

    .profile-picture-preview {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid var(--cw-brand-accent);
        transition: all 0.3s ease;
        display: block;
    }

    .profile-picture-preview:hover {
        border-color: var(--cw-brand-primary);
    }

    .profile-picture-placeholder {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: var(--cw-gradient-card-subtle);
        border: 2px solid var(--cw-brand-accent);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--cw-neutral-600);
        transition: all 0.3s ease;
    }

    .profile-picture-placeholder:hover {
        border-color: var(--cw-brand-primary);
        background: var(--cw-brand-accent);
    }

    .profile-picture-upload-section {
        display: flex;
        align-items: flex-start;
        gap: 2rem;
        flex-wrap: wrap;
    }

    .profile-picture-upload-controls {
        flex: 1;
        min-width: 280px;
    }

    .profile-picture-upload-help {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 1rem;
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(47, 22, 15, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 1rem 2rem;
        background: white;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-family: var(--cw-font-heading);
        letter-spacing: 0.025em;
        gap: 0.5rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: var(--cw-shadow-md);
    }

    /* Error Messages */
    .invalid-feedback {
        display: block !important;
        width: 100%;
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #dc2626;
        font-weight: 500;
        font-family: var(--cw-font-primary);
    }

    .invalid-feedback i {
        margin-right: 0.25rem;
    }

    /* Form Text */
    .form-text {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: var(--cw-neutral-600);
        font-family: var(--cw-font-primary);
    }

    /* Alert Styling */
    .alert-cw-error {
        background: #fef2f2;
        border: 1px solid #fecaca;
        color: #991b1b;
        border-radius: 0.5rem;
        padding: 1rem 1.25rem;
        margin-bottom: 1rem;
        font-family: var(--cw-font-primary);
    }

    /* Form Actions */
    .form-actions {
        border-top: 2px solid var(--cw-brand-accent);
        padding-top: 2rem;
        margin-top: 2rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .profile-edit-section {
            padding: 3rem 0;
        }

        .profile-edit-container {
            padding: 0 1.5rem;
        }

        .profile-edit-header {
            padding: 2rem 2rem 1.5rem;
        }

        .edit-title {
            font-size: 2.25rem;
        }

        .edit-subtitle {
            font-size: 1.125rem;
        }

        .profile-edit-body {
            padding: 2rem;
        }

        .btn-cw-primary,
        .btn-cw-secondary {
            width: 100%;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .form-actions {
            text-align: center;
        }

        .profile-picture-upload-section {
            flex-direction: column;
            align-items: center;
            text-align: center;
            gap: 1.5rem;
        }

        .profile-picture-upload-controls {
            min-width: auto;
            width: 100%;
            text-align: left;
        }

        .profile-picture-preview-container {
            margin-bottom: 0;
        }
    }

    @media (max-width: 576px) {
        .profile-edit-container {
            padding: 0 1rem;
        }

        .profile-edit-header {
            padding: 1.5rem 1.5rem 1rem;
        }

        .profile-edit-body {
            padding: 1.5rem;
        }

        .edit-title {
            font-size: 1.875rem;
        }

        .edit-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Profile picture preview
    const profilePictureInput = document.getElementById('{{ form.profile_picture.id_for_label }}');
    if (profilePictureInput) {
        profilePictureInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('profile-picture-preview');
                    const container = preview.parentElement;

                    // Remove existing content
                    preview.remove();

                    // Create new image element
                    const img = document.createElement('img');
                    img.id = 'profile-picture-preview';
                    img.src = e.target.result;
                    img.alt = 'Profile Picture';
                    img.className = 'profile-picture-preview';

                    // Add to container
                    container.appendChild(img);
                };
                reader.readAsDataURL(this.files[0]);
            }
        });
    }
});
</script>
{% endblock %}

{% block content %}
<section class="profile-edit-section">
    <div class="profile-edit-container">
        <div class="profile-edit-card">
            <div class="profile-edit-header">
                <div class="content">
                    <div class="edit-icon">
                        <i class="fas fa-user-edit"></i>
                    </div>
                    <h1 class="edit-title">Edit Profile</h1>
                    <p class="edit-subtitle">Update your personal information and preferences</p>
                </div>
            </div>
            <div class="profile-edit-body">

<form method="post" enctype="multipart/form-data" novalidate>
  {% csrf_token %}

  {% if form.non_field_errors %}
  <div class="alert-cw-error mb-4">
    {% for error in form.non_field_errors %}
    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
    {% endfor %}
  </div>
  {% endif %}

  <!-- Profile Picture Section -->
  <div class="mb-4">
    <h5 class="section-header">
      <i class="fas fa-camera"></i>Profile Picture
    </h5>
    <div class="profile-picture-upload-section">
      <div>
        <div class="profile-picture-preview-container">
          {% if form.instance.profile_picture %}
            <img id="profile-picture-preview" src="{{ form.instance.profile_picture.url }}" alt="Profile Picture" class="profile-picture-preview">
          {% else %}
            <div id="profile-picture-preview" class="profile-picture-placeholder">
              <i class="fas fa-user fa-3x"></i>
            </div>
          {% endif %}
        </div>
      </div>
      <div class="profile-picture-upload-controls">
        <label class="form-label" for="{{ form.profile_picture.id_for_label }}">Choose Profile Picture</label>
        {{ form.profile_picture|add_class:"form-control-cw" }}
        {% if form.profile_picture.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.profile_picture.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
        <div class="profile-picture-upload-help">
          <div class="mb-2">
            <strong><i class="fas fa-info-circle me-1"></i>Upload Guidelines:</strong>
          </div>
          <ul class="mb-0 ps-3">
            <li>Recommended size: 400x400 pixels or larger</li>
            <li>Accepted formats: JPG, PNG, GIF</li>
            <li>Maximum file size: 5MB</li>
            <li>Square images work best for circular display</li>
          </ul>
        </div>
        {% if form.profile_picture.help_text %}
        <div class="form-text mt-2">{{ form.profile_picture.help_text }}</div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Personal Information Section -->
  <div class="mb-4">
    <h5 class="section-header">
      <i class="fas fa-user"></i>Personal Information
    </h5>
    <div class="row">
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.first_name.id_for_label }}">{{ form.first_name.label }}</label>
        {{ form.first_name|add_class:"form-control-cw" }}
        {% if form.first_name.errors %}
        <div class="invalid-feedback d-block" role="alert" aria-live="polite">
          {% for error in form.first_name.errors %}
          <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.last_name.id_for_label }}">{{ form.last_name.label }}</label>
        {{ form.last_name|add_class:"form-control-cw" }}
        {% if form.last_name.errors %}
        <div class="invalid-feedback d-block" role="alert" aria-live="polite">
          {% for error in form.last_name.errors %}
          <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.phone_number.id_for_label }}">{{ form.phone_number.label }}</label>
        {{ form.phone_number|add_class:"form-control-cw"|attr:"aria-describedby:phone_help" }}
        {% if form.phone_number.errors %}
        <div class="invalid-feedback d-block" role="alert" aria-live="polite">
          {% for error in form.phone_number.errors %}
          <i class="fas fa-exclamation-circle me-1" aria-hidden="true"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
        {% if form.phone_number.help_text %}
        <div class="form-text" id="phone_help">{{ form.phone_number.help_text }}</div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.gender.id_for_label }}">{{ form.gender.label }}</label>
        {{ form.gender|add_class:"form-select-cw" }}
        {% if form.gender.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.gender.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.birth_month.id_for_label }}">{{ form.birth_month.label }}</label>
        {{ form.birth_month|add_class:"form-select-cw" }}
        {% if form.birth_month.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.birth_month.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.birth_year.id_for_label }}">{{ form.birth_year.label }}</label>
        {{ form.birth_year|add_class:"form-select-cw" }}
        {% if form.birth_year.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.birth_year.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Address Information Section -->
  <div class="mb-4">
    <h5 class="section-header">
      <i class="fas fa-map-marker-alt"></i>Address Information
    </h5>
    <div class="row">
      <div class="col-12 mb-3">
        <label class="form-label" for="{{ form.address.id_for_label }}">{{ form.address.label }}</label>
        {{ form.address|add_class:"form-control-cw" }}
        {% if form.address.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.address.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.city.id_for_label }}">{{ form.city.label }}</label>
        {{ form.city|add_class:"form-control-cw" }}
        {% if form.city.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.city.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
      <div class="col-md-6 mb-3">
        <label class="form-label" for="{{ form.zip_code.id_for_label }}">{{ form.zip_code.label }}</label>
        {{ form.zip_code|add_class:"form-control-cw" }}
        {% if form.zip_code.errors %}
        <div class="invalid-feedback d-block">
          {% for error in form.zip_code.errors %}
          <i class="fas fa-exclamation-circle me-1"></i>{{ error }}
          {% endfor %}
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="form-actions">
    <div class="d-flex justify-content-between flex-wrap gap-3">
      <a href="{% url 'accounts_app:customer_profile' %}" class="btn-cw-secondary">
        <i class="fas fa-arrow-left"></i>Cancel
      </a>
      <button type="submit" class="btn-cw-primary">
        <i class="fas fa-save"></i>Save Changes
      </button>
    </div>
  </div>
</form>
            </div>
        </div>
    </div>
</section>
{% endblock %}
