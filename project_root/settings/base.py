# --- Standard Library Imports ---
import os
import sys
from pathlib import Path
import dj_database_url
import environ

# --- Local App Imports ---
from config.logging import LOGGING

# --- Environment Configuration ---
env = environ.Env(
    # Set casting and default values
    DEBUG=(bool, False),
    SECRET_KEY=(str, ''),
    LOG_LEVEL=(str, 'INFO'),
    PLATFORM_FEE_RATE=(float, 0.05),
    DASHBOARD_CACHE_TIMEOUT=(int, 300),
    NOTIFICATION_CACHE_TIMEOUT=(int, 60),
    EMAIL_PORT=(int, 587),
    EMAIL_USE_TLS=(bool, True),
    FORCE_EMAIL_BACKEND=(bool, False),
)

# --- Base Directory ---
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# --- Read Environment File ---
environ.Env.read_env(BASE_DIR / '.env')



# --- Environment Validation ---
def validate_environment():
    """Validate required environment variables with detailed error messages."""
    errors = []
    warnings = []

    # Required variables for all environments
    required_vars = {
        'SECRET_KEY': 'Django secret key for cryptographic signing',
    }

    # Check required variables
    for var, description in required_vars.items():
        value = env(var, default=None)
        if not value:
            errors.append(f"  - {var}: {description}")
        elif var == 'SECRET_KEY' and value == 'django-insecure-change-this-in-production':
            warnings.append(f"  - {var}: Using default insecure key. Generate a new one for production!")

    # Environment-specific validation
    django_env = env('DJANGO_ENVIRONMENT', default='development')

    if django_env == 'production':
        production_vars = {
            'DATABASE_URL': 'PostgreSQL database connection string',
            'EMAIL_HOST_PASSWORD': 'SendGrid API key for email sending',
            'AWS_ACCESS_KEY_ID': 'AWS access key for S3 storage',
            'AWS_SECRET_ACCESS_KEY': 'AWS secret key for S3 storage',
            'AWS_STORAGE_BUCKET_NAME': 'S3 bucket name for file storage',
        }

        for var, description in production_vars.items():
            value = env(var, default=None)
            if not value:
                errors.append(f"  - {var}: {description} (required in production)")

    # Check for common configuration issues
    debug_mode = env('DEBUG', default=False)
    if django_env == 'production' and debug_mode:
        warnings.append("  - DEBUG: Debug mode is enabled in production environment")

    # Report validation results
    if errors:
        error_msg = f"""
Environment Validation Failed!

Missing required environment variables for '{django_env}' environment:
{chr(10).join(errors)}

Please check your .env file or environment variables.
See .env.example for a complete list of available variables.
"""
        raise environ.ImproperlyConfigured(error_msg)

    if warnings:
        warning_msg = f"""
Environment Validation Warnings for '{django_env}' environment:
{chr(10).join(warnings)}
"""
        print(f"\033[93m{warning_msg}\033[0m")  # Yellow warning text

# Validate environment on import
validate_environment()

# --- Logs Configuration ---
LOG_LEVEL = env('LOG_LEVEL').upper()

# --- Core Configuration ---
SECRET_KEY = env('SECRET_KEY')
DEBUG = env('DEBUG')
PLATFORM_FEE_RATE = env('PLATFORM_FEE_RATE')
DASHBOARD_CACHE_TIMEOUT = env('DASHBOARD_CACHE_TIMEOUT')
NOTIFICATION_CACHE_TIMEOUT = env('NOTIFICATION_CACHE_TIMEOUT')
ENABLE_TEST_VIEW = DEBUG



# --- Allowed Hosts & CSRF ---
ALLOWED_HOSTS = ['.cozywish.com', 'cozywish.onrender.com']
RENDER_EXTERNAL_HOSTNAME = os.environ.get('RENDER_EXTERNAL_HOSTNAME')
if RENDER_EXTERNAL_HOSTNAME:
    ALLOWED_HOSTS.append(RENDER_EXTERNAL_HOSTNAME)
if DEBUG:
    ALLOWED_HOSTS.extend(['localhost', '127.0.0.1', 'testserver'])

CSRF_TRUSTED_ORIGINS = [
    'https://www.cozywish.com',
    'https://cozywish.onrender.com'
]




# --- Installed Applications ---
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',  # For humanize template tags

    # Third-party apps
    'storages',
    'widget_tweaks',
    'crispy_forms',
    'crispy_bootstrap5',  # Upgraded from crispy_bootstrap4
    'formtools',
    'django_htmx',
    'django_cleanup',
    'compressor',  # Static file compression

    # Authentication & Security apps
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.google',
    'allauth.socialaccount.providers.apple',
    'axes',  # Brute force protection
    'django_otp',  # Two-factor authentication
    'django_otp.plugins.otp_totp',
    'django_otp.plugins.otp_static',
    'csp',
    'corsheaders',

    # Project apps
    'accounts_app',
    'utility_app',
    'venues_app',
    'discount_app',
    'booking_cart_app',
    'payments_app',
    'dashboard_app',
    'review_app',
    'notifications_app',
    'admin_app',
    'utils',
]



# --- Middleware ---
MIDDLEWARE = [
    # CORS headers must be first to handle preflight requests
    'corsheaders.middleware.CorsMiddleware',

    # Security middleware should be early in the stack
    'django.middleware.security.SecurityMiddleware',

    # Axes middleware for brute force protection (after security)
    'axes.middleware.AxesMiddleware',

    # CSP middleware for Content Security Policy
    'csp.middleware.CSPMiddleware',

    # WhiteNoise for static files (after security)
    'whitenoise.middleware.WhiteNoiseMiddleware',

    # Session middleware
    'django.contrib.sessions.middleware.SessionMiddleware',

    # Common middleware for URL processing
    'django.middleware.common.CommonMiddleware',

    # CSRF protection
    'django.middleware.csrf.CsrfViewMiddleware',

    # Authentication
    'django.contrib.auth.middleware.AuthenticationMiddleware',

    # Allauth middleware (after authentication)
    'allauth.account.middleware.AccountMiddleware',

    # OTP middleware for two-factor authentication (after auth)
    'django_otp.middleware.OTPMiddleware',

    # Messages framework
    'django.contrib.messages.middleware.MessageMiddleware',

    # HTMX middleware for dynamic interactions
    'django_htmx.middleware.HtmxMiddleware',

    # Clickjacking protection
    'django.middleware.clickjacking.XFrameOptionsMiddleware',

    # Custom admin CSP middleware (after CSP middleware)
    'admin_app.middleware.AdminCSPMiddleware',

    # Dashboard access control (last)
    'dashboard_app.middleware.DashboardAccessMiddleware',
]



# --- URL Configuration ---
ROOT_URLCONF = 'project_root.urls'
WSGI_APPLICATION = 'project_root.wsgi.application'



# --- Templates ---
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'admin_app.context_processors.recent_admin_links',
                'booking_cart_app.context_processors.cart_context',
                'notifications_app.context_processors.notifications_context',
                'dashboard_app.context_processors.provider_context',
            ],
        },
    },
]



# --- Database Configuration ---
DATABASE_URL = env('DATABASE_URL', default=None)
if DATABASE_URL:
    DATABASES = {'default': dj_database_url.parse(DATABASE_URL, conn_max_age=600)}
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }


# --- Test Database Configuration ---
if 'test' in sys.argv:
    DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }


# --- Password Validation ---
AUTH_PASSWORD_VALIDATORS = [
    {'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator'},
    {'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator'},
    {'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator'},
    {'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator'},
]


# --- Internationalization ---
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True


# --- Testing Flag ---
TESTING = 'test' in sys.argv


# --- Static Files Configuration ---
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']

# Add compressor finder to staticfiles finders
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
    'compressor.finders.CompressorFinder',
]

if not DEBUG:
    STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
    WHITENOISE_USE_FINDERS = True
    WHITENOISE_AUTOREFRESH = False  # Disable in production for performance

    # Advanced WhiteNoise settings for production
    WHITENOISE_MAX_AGE = 31536000  # 1 year cache for static files
    WHITENOISE_SKIP_COMPRESS_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'zip', 'gz', 'tgz', 'bz2', 'tbz', 'xz', 'br']
    WHITENOISE_IMMUTABLE_FILE_TEST = lambda path, url: True  # Mark all files as immutable for better caching
else:
    # Development settings
    WHITENOISE_USE_FINDERS = True
    WHITENOISE_AUTOREFRESH = True


# --- Django Compressor Configuration ---
COMPRESS_ENABLED = not DEBUG  # Enable compression in production
COMPRESS_OFFLINE = not DEBUG  # Pre-compress files in production
COMPRESS_CSS_FILTERS = [
    'compressor.filters.css_default.CssAbsoluteFilter',
    'compressor.filters.cssmin.rCSSMinFilter',
]
COMPRESS_JS_FILTERS = [
    'compressor.filters.jsmin.rJSMinFilter',
]

# Compressor storage and URL settings
COMPRESS_STORAGE = 'compressor.storage.CompressorFileStorage'
COMPRESS_URL = STATIC_URL
COMPRESS_ROOT = STATIC_ROOT

# Cache settings for compressor
COMPRESS_CACHE_BACKEND = 'default'
COMPRESS_CACHE_KEY_FUNCTION = 'compressor.cache.simple_cachekey'

# Output directory for compressed files
COMPRESS_OUTPUT_DIR = 'CACHE'

# Rebuild compressed files when source files change
COMPRESS_REBUILD_TIMEOUT = 2592000  # 30 days

# Advanced compression settings
COMPRESS_PRECOMPILERS = (
    ('text/coffeescript', 'coffee --compile --stdio'),
    ('text/less', 'lessc {infile} {outfile}'),
    ('text/x-sass', 'sass {infile} {outfile}'),
    ('text/x-scss', 'sass --scss {infile} {outfile}'),
)

# Compression parser settings
COMPRESS_PARSER = 'compressor.parser.AutoSelectParser'

# Enable compression for development (optional)
# COMPRESS_ENABLED = True  # Uncomment to enable compression in development

# File extension handling
COMPRESS_CSS_HASHING_METHOD = 'mtime'
COMPRESS_JS_HASHING_METHOD = 'mtime'

# Offline compression settings
COMPRESS_OFFLINE_CONTEXT = {
    'STATIC_URL': STATIC_URL,
}

# Compression manifest settings
COMPRESS_OFFLINE_MANIFEST = 'manifest.json'


# --- Media Files Configuration ---
if DEBUG:
    MEDIA_URL = '/media/'
    MEDIA_ROOT = BASE_DIR / 'media'
    # Use default file system storage for development
    STORAGES = {
        "default": {
            "BACKEND": "django.core.files.storage.FileSystemStorage",
        },
        "staticfiles": {
            "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
        },
    }
else:
    # AWS S3 Configuration
    AWS_ACCESS_KEY_ID = env('AWS_ACCESS_KEY_ID', default=None)
    AWS_SECRET_ACCESS_KEY = env('AWS_SECRET_ACCESS_KEY', default=None)
    AWS_STORAGE_BUCKET_NAME = env('AWS_STORAGE_BUCKET_NAME', default=None)
    AWS_S3_REGION_NAME = env('AWS_S3_REGION_NAME', default='us-east-1')
    AWS_S3_CUSTOM_DOMAIN = env('AWS_S3_CUSTOM_DOMAIN', default=None)

    if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY and AWS_STORAGE_BUCKET_NAME:
        # Django 4.2+ STORAGES configuration (replaces DEFAULT_FILE_STORAGE)
        STORAGES = {
            "default": {
                "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
                "OPTIONS": {
                    "access_key": AWS_ACCESS_KEY_ID,
                    "secret_key": AWS_SECRET_ACCESS_KEY,
                    "bucket_name": AWS_STORAGE_BUCKET_NAME,
                    "region_name": AWS_S3_REGION_NAME,
                    "custom_domain": AWS_S3_CUSTOM_DOMAIN,
                    "file_overwrite": False,
                    "default_acl": None,
                    "signature_version": "s3v4",
                    "addressing_style": "virtual",
                    "use_ssl": True,
                    "verify": True,
                    "object_parameters": {
                        "CacheControl": "max-age=86400",
                    },
                    "querystring_auth": True,
                    "querystring_expire": 3600,  # 1 hour
                },
            },
            "staticfiles": {
                "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
            },
        }

        # Legacy AWS settings for backward compatibility (some packages may still use these)
        # Note: DEFAULT_FILE_STORAGE is deprecated in Django 4.2+ in favor of STORAGES
        AWS_S3_FILE_OVERWRITE = False
        AWS_DEFAULT_ACL = None
        AWS_S3_VERIFY = True
        AWS_S3_USE_SSL = True
        AWS_S3_SIGNATURE_VERSION = 's3v4'
        AWS_S3_ADDRESSING_STYLE = 'virtual'
        AWS_QUERYSTRING_AUTH = True
        AWS_QUERYSTRING_EXPIRE = 3600  # 1 hour

        # Performance and caching
        AWS_S3_OBJECT_PARAMETERS = {
            'CacheControl': 'max-age=86400',
        }

        # URL configuration
        if AWS_S3_CUSTOM_DOMAIN:
            MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/'
        else:
            MEDIA_URL = f'https://{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com/'

        # Ensure URLs don't have double slashes
        if not MEDIA_URL.endswith('/'):
            MEDIA_URL += '/'

    else:
        # Log missing credentials for debugging
        missing_creds = []
        if not AWS_ACCESS_KEY_ID:
            missing_creds.append('AWS_ACCESS_KEY_ID')
        if not AWS_SECRET_ACCESS_KEY:
            missing_creds.append('AWS_SECRET_ACCESS_KEY')
        if not AWS_STORAGE_BUCKET_NAME:
            missing_creds.append('AWS_STORAGE_BUCKET_NAME')

        error_msg = f"AWS S3 credentials are required in production. Missing: {', '.join(missing_creds)}"
        print(f"WARNING: {error_msg}")  # Log to console instead of raising exception

        # Fallback to local storage with warning
        MEDIA_URL = '/media/'
        MEDIA_ROOT = BASE_DIR / 'media'
        STORAGES = {
            "default": {
                "BACKEND": "django.core.files.storage.FileSystemStorage",
            },
            "staticfiles": {
                "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
            },
        }


# --- Email Configuration (SendGrid) ---
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD', default='')
FORCE_EMAIL_BACKEND = env('FORCE_EMAIL_BACKEND', default=False)

# Email backend logic:
# 1. If FORCE_EMAIL_BACKEND is True, use SMTP even in debug mode
# 2. If EMAIL_HOST_PASSWORD is set and we're not in test mode, use SMTP
# 3. Otherwise, use console backend for development
if TESTING:
    # Always use console backend for tests
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
elif FORCE_EMAIL_BACKEND or (EMAIL_HOST_PASSWORD and not DEBUG):
    # Use SMTP backend for production or when forced
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
elif EMAIL_HOST_PASSWORD and DEBUG:
    # In development with SendGrid configured, ask user what they prefer
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
else:
    # Default to console backend for development without email config
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

EMAIL_HOST = env('EMAIL_HOST', default='smtp.sendgrid.net')
EMAIL_PORT = env('EMAIL_PORT')
EMAIL_USE_TLS = env('EMAIL_USE_TLS')
EMAIL_HOST_USER = env('EMAIL_HOST_USER', default='apikey')
DEFAULT_FROM_EMAIL = env('DEFAULT_FROM_EMAIL', default='<EMAIL>')
SERVER_EMAIL = env('SERVER_EMAIL', default='<EMAIL>')
EMAIL_TIMEOUT = 30


# --- User Model and Primary Key ---
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
AUTH_USER_MODEL = 'accounts_app.CustomUser'


# --- Authentication Configuration ---
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
    'axes.backends.AxesBackend',  # Must be last
]

# --- Authentication URLs ---
LOGIN_URL = '/accounts/login/'
LOGOUT_REDIRECT_URL = '/'

# --- Django-Allauth Configuration ---
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_AUTHENTICATION_METHOD = 'email'
ACCOUNT_EMAIL_VERIFICATION = 'mandatory'
ACCOUNT_EMAIL_CONFIRMATION_EXPIRE_DAYS = 3
ACCOUNT_LOGIN_ATTEMPTS_LIMIT = 5
ACCOUNT_LOGIN_ATTEMPTS_TIMEOUT = 300  # 5 minutes
ACCOUNT_LOGOUT_ON_GET = False
ACCOUNT_LOGOUT_REDIRECT_URL = '/'
ACCOUNT_SESSION_REMEMBER = True
ACCOUNT_SIGNUP_PASSWORD_ENTER_TWICE = True
ACCOUNT_UNIQUE_EMAIL = True
ACCOUNT_USER_MODEL_USERNAME_FIELD = None
ACCOUNT_USER_MODEL_EMAIL_FIELD = 'email'

# Custom adapters
ACCOUNT_ADAPTER = 'accounts_app.adapters.CustomAccountAdapter'
SOCIALACCOUNT_ADAPTER = 'accounts_app.adapters.CustomSocialAccountAdapter'

# Social account providers
SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'SCOPE': [
            'profile',
            'email',
        ],
        'AUTH_PARAMS': {
            'access_type': 'online',
        },
        'OAUTH_PKCE_ENABLED': True,
    },
    'apple': {
        'APP': {
            'client_id': env('APPLE_CLIENT_ID', default=''),
            'secret': env('APPLE_SECRET', default=''),
            'key': env('APPLE_KEY_ID', default=''),
            'certificate_key': env('APPLE_PRIVATE_KEY', default=''),
        }
    }
}

# --- Django-Axes Configuration (Brute Force Protection) ---
AXES_ENABLED = True
AXES_FAILURE_LIMIT = 5
AXES_COOLOFF_TIME = 1  # 1 hour
AXES_RESET_ON_SUCCESS = True
AXES_LOCKOUT_TEMPLATE = 'accounts_app/account_locked.html'
AXES_LOCKOUT_URL = '/accounts/locked/'
AXES_USE_USER_AGENT = True
AXES_LOCK_OUT_BY_COMBINATION_USER_AND_IP = True
AXES_ENABLE_ADMIN = True

# --- Django-OTP Configuration (Two-Factor Authentication) ---
OTP_TOTP_ISSUER = 'CozyWish'
OTP_LOGIN_URL = '/accounts/login/'



# --- Cache Configuration ---
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'cozywish-cache',
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
            'CULL_FREQUENCY': 3,
        }
    }
}

# --- Celery Configuration ---
CELERY_BROKER_URL = env('CELERY_BROKER_URL', default='redis://localhost:6379/0')
CELERY_RESULT_BACKEND = env('CELERY_RESULT_BACKEND', default='redis://localhost:6379/0')
if 'test' in sys.argv:
    CELERY_TASK_ALWAYS_EAGER = True
    CELERY_TASK_EAGER_PROPAGATES = True


# --- Crispy Forms Configuration ---
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# --- HTMX Configuration ---
# HTMX settings for dynamic interactions
HTMX_CSRF_HEADER_NAME = 'X-CSRFToken'

# --- Django Form Tools Configuration ---
# Session-based form wizard storage
FORM_RENDERER = 'django.forms.renderers.TemplatesSetting'


# --- Content Security Policy (CSP) Configuration ---
# Base CSP settings - can be overridden in environment-specific settings
CSP_DEFAULT_SRC = ("'self'",)
CSP_SCRIPT_SRC = (
    "'self'",
    "'unsafe-inline'",  # Required for some admin functionality and HTMX
    "https://cdn.jsdelivr.net",  # CDN for libraries
    "https://cdnjs.cloudflare.com",  # CDN for libraries
    "https://unpkg.com",  # HTMX CDN
)
CSP_STYLE_SRC = (
    "'self'",
    "'unsafe-inline'",  # Required for admin and some styling
    "https://fonts.googleapis.com",  # Google Fonts
    "https://cdn.jsdelivr.net",  # CDN for CSS libraries
    "https://cdnjs.cloudflare.com",  # CDN for CSS libraries
)
CSP_FONT_SRC = (
    "'self'",
    "https://fonts.gstatic.com",  # Google Fonts
    "data:",  # Data URLs for fonts
)
CSP_IMG_SRC = (
    "'self'",
    "data:",  # Data URLs for images
    "https:",  # Allow HTTPS images (for user uploads, external images)
)
CSP_CONNECT_SRC = (
    "'self'",
    # Add your API endpoints here
)
CSP_FRAME_SRC = (
    "'none'",  # Prevent framing by default
)
CSP_OBJECT_SRC = ("'none'",)  # Prevent object/embed tags
CSP_BASE_URI = ("'self'",)  # Restrict base tag
CSP_FORM_ACTION = ("'self'",)  # Restrict form submissions

# CSP reporting (can be enabled in production)
# CSP_REPORT_URI = '/csp-report/'
CSP_INCLUDE_NONCE_IN = ['script-src', 'style-src']  # Enable nonces for scripts/styles


# --- Rate Limiting Configuration ---
# Django-ratelimit settings
RATELIMIT_ENABLE = True
RATELIMIT_USE_CACHE = 'default'

# Rate limiting rules (can be overridden in environment-specific settings)
RATELIMIT_LOGIN_ATTEMPTS = '5/5m'  # 5 attempts per 5 minutes
RATELIMIT_API_CALLS = '100/h'      # 100 API calls per hour
RATELIMIT_FORM_SUBMISSIONS = '10/m'  # 10 form submissions per minute
RATELIMIT_PASSWORD_RESET = '3/h'   # 3 password reset attempts per hour
RATELIMIT_REGISTRATION = '5/h'     # 5 registration attempts per hour

# Rate limiting view configuration
RATELIMIT_VIEW = 'django_ratelimit.views.ratelimited'


# --- CORS Configuration ---
# Base CORS settings - can be overridden in environment-specific settings
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    # Add your frontend domains here
    # "https://yourdomain.com",
    # "https://www.yourdomain.com",
]

CORS_ALLOWED_ORIGIN_REGEXES = [
    # Add regex patterns for dynamic subdomains if needed
    # r"^https://\w+\.yourdomain\.com$",
]

CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# CORS preflight cache
CORS_PREFLIGHT_MAX_AGE = 86400  # 24 hours


# --- Security Headers Configuration ---
# Basic security headers (applied in all environments)
SECURE_CONTENT_TYPE_NOSNIFF = True  # Prevent MIME type sniffing
SECURE_BROWSER_XSS_FILTER = True    # Enable XSS filtering
X_FRAME_OPTIONS = 'DENY'            # Prevent clickjacking

# Referrer Policy - control referrer information
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# Additional security headers via custom middleware or django-security
SECURE_CROSS_ORIGIN_OPENER_POLICY = 'same-origin'

# Cookie security settings
SESSION_COOKIE_HTTPONLY = True      # Prevent JavaScript access to session cookies
CSRF_COOKIE_HTTPONLY = True         # Prevent JavaScript access to CSRF cookies
SESSION_COOKIE_SAMESITE = 'Lax'     # CSRF protection
CSRF_COOKIE_SAMESITE = 'Lax'        # CSRF protection

# --- Production Security Settings ---
if not DEBUG:
    # SSL/HTTPS settings
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True

    # HSTS (HTTP Strict Transport Security)
    SECURE_HSTS_SECONDS = 31536000      # 1 year
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True

    # Additional production security
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

    # Permissions Policy (formerly Feature Policy)
    PERMISSIONS_POLICY = {
        'accelerometer': [],
        'ambient-light-sensor': [],
        'autoplay': [],
        'battery': [],
        'camera': [],
        'cross-origin-isolated': [],
        'display-capture': [],
        'document-domain': [],
        'encrypted-media': [],
        'execution-while-not-rendered': [],
        'execution-while-out-of-viewport': [],
        'fullscreen': [],
        'geolocation': [],
        'gyroscope': [],
        'magnetometer': [],
        'microphone': [],
        'midi': [],
        'navigation-override': [],
        'payment': [],
        'picture-in-picture': [],
        'publickey-credentials-get': [],
        'screen-wake-lock': [],
        'sync-xhr': [],
        'usb': [],
        'web-share': [],
        'xr-spatial-tracking': [],
    }


# --- Forms URL Field Configuration ---
FORMS_URLFIELD_ASSUME_HTTPS = True







"""
## Production Environment Variables
AWS_ACCESS_KEY_ID
AWS_S3_CUSTOM_DOMAIN
AWS_S3_REGION_NAME
AWS_SECRET_ACCESS_KEY
AWS_STORAGE_BUCKET_NAME
DATABASE_URL
DEBUG
EMAIL_HOST
EMAIL_HOST_PASSWORD
EMAIL_HOST_USER
EMAIL_PORT
EMAIL_USE_TLS
LOG_LEVEL
SECRET_KEY
WEB_CONCURRENCY
"""