# --- Django Imports ---
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.http import HttpResponse
from django.urls import include, path

# --- Local App Imports ---
from admin_app.views.common import home_view


def favicon_view(request):
    """Return empty response for favicon.ico to prevent 404 errors."""
    return HttpResponse(status=204)  # No Content


# --- URL Configuration ---
urlpatterns = [
    path("admin/", admin.site.urls),  # Django admin dashboard
    path("admin-panel/", include("admin_app.urls")),  # Custom admin panel
    path("accounts/", include("accounts_app.urls")),  # User authentication and profiles
    path("auth/", include("allauth.urls")),  # Django-allauth authentication URLs
    path("venues/", include("venues_app.urls")),  # Venue management and search
    path("discounts/", include("discount_app.urls")),  # Discount management
    path("bookings/", include("booking_cart_app.urls")),  # Booking and cart management
    path("payments/", include("payments_app.urls")),  # Payment processing and refunds
    path(
        "dashboard/", include("dashboard_app.urls")
    ),  # Customer and provider dashboards
    path("reviews/", include("review_app.urls")),  # Review and rating management
    path(
        "notifications/", include("notifications_app.urls")
    ),  # Notification management
    path("", home_view, name="home"),  # Main landing page (admin_app home view)
    path("utility/", include("utility_app.urls")),  # Public landing pages and utilities
    path("favicon.ico", favicon_view),  # Favicon handler to prevent 404 errors
]


# --- Media and Static Files Configuration ---
if settings.DEBUG:
    # Serve media files (user uploads) during development
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

    # Serve static files (CSS, JS, images) during development
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)


# --- Error Handlers ---
handler404 = "utils.error_handlers.custom_404_view"
handler500 = "utils.error_handlers.custom_500_view"
handler403 = "utils.error_handlers.custom_403_view"
handler400 = "utils.error_handlers.custom_400_view"
