# --- Standard Library Imports ---
from datetime import timedelta

# --- Django Imports ---
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.core.validators import RegexValidator
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from .utils import (
    get_customer_profile_image_path,
    get_provider_profile_image_path,
    get_staff_profile_image_path,
)

# --- Custom User Management ---


class CustomUserManager(BaseUserManager):
    """Custom user manager using email as unique identifier instead of username"""

    def create_user(
        self, email: str, password: str = None, **extra_fields
    ) -> "CustomUser":
        """
        Create and save a regular user with the given email and password.

        :param email: User's email address
        :param password: User's password
        :param extra_fields: Additional user fields
        :return: CustomUser instance
        :raises ValueError: If email is not provided
        """
        if not email:
            raise ValueError(_("The Email field must be set"))

        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(
        self, email: str, password: str = None, **extra_fields
    ) -> "CustomUser":
        """
        Create and save a superuser with the given email and password.

        :param email: Superuser's email address
        :param password: Superuser's password
        :param extra_fields: Additional superuser fields
        :return: CustomUser instance
        :raises ValueError: If required superuser flags are missing
        """
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_active", True)

        if not extra_fields.get("is_staff"):
            raise ValueError(_("Superuser must have is_staff=True."))
        if not extra_fields.get("is_superuser"):
            raise ValueError(_("Superuser must have is_superuser=True."))

        return self.create_user(email, password, **extra_fields)


class CustomUser(AbstractUser):
    """Custom authentication model using email as unique identifier with role-based access"""

    # Role constants and choices
    ROLES = (
        ("customer", _("Customer")),
        ("service_provider", _("Service Provider")),
        ("admin", _("Admin")),
    )
    CUSTOMER, SERVICE_PROVIDER, ADMIN = ROLES[0][0], ROLES[1][0], ROLES[2][0]

    # Authentication configuration
    username = None
    email = models.EmailField(
        _("email address"),
        unique=True,
        error_messages={"unique": _("A user with this email already exists.")},
    )
    role = models.CharField(_("role"), max_length=20, choices=ROLES, default=CUSTOMER)
    date_joined = models.DateTimeField(_("date joined"), default=timezone.now)

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []
    objects = CustomUserManager()

    class Meta:
        verbose_name = _("User")
        verbose_name_plural = _("Users")
        ordering = ["-date_joined"]
        indexes = [
            models.Index(fields=["email"]),
            models.Index(fields=["role"]),
        ]

    def __str__(self) -> str:
        return self.email

    @property
    def full_name(self) -> str:
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def short_name(self) -> str:
        return self.first_name

    # Dynamic role properties
    def _role_property(role_value):
        @property
        def prop(self):
            return self.role == role_value

        return prop

    # Role properties
    is_customer = _role_property(CUSTOMER)
    is_service_provider = _role_property(SERVICE_PROVIDER)
    is_admin = _role_property(ADMIN)


# --- Login History & Security Alerts ---


class LoginHistory(models.Model):
    """Audit trail for authentication attempts with threat detection"""

    user = models.ForeignKey(
        "CustomUser", on_delete=models.CASCADE, related_name="login_history"
    )
    timestamp = models.DateTimeField(_("timestamp"), auto_now_add=True)
    ip_address = models.GenericIPAddressField(_("IP address"))
    user_agent = models.TextField(_("user agent"), blank=True)
    is_successful = models.BooleanField(_("successful"), default=False)

    class Meta:
        verbose_name = _("Login History")
        verbose_name_plural = _("Login History Records")
        ordering = ["-timestamp"]
        indexes = [
            models.Index(fields=["timestamp"], name="login_timestamp_idx"),
            models.Index(fields=["ip_address"], name="login_ip_idx"),
            models.Index(fields=["is_successful"], name="login_success_idx"),
        ]

    def __str__(self):
        return f"{self.user.email} @ {self.timestamp}"

    @classmethod
    def purge_old_records(cls, days=90):
        """Remove records older than specified days"""
        cutoff = timezone.now() - timedelta(days=days)
        cls.objects.filter(timestamp__lt=cutoff).delete()

    @classmethod
    def detect_suspicious_activity(cls, ip_address, user=None):
        """Identify and alert on suspicious login patterns"""
        time_window = timezone.now() - timedelta(hours=1)
        failed_attempts = cls.objects.filter(
            ip_address=ip_address, is_successful=False, timestamp__gte=time_window
        ).count()

        # Threshold configuration
        thresholds = {5: LoginAlert.HIGH, 3: LoginAlert.MEDIUM}

        for count, severity in thresholds.items():
            if failed_attempts >= count:
                LoginAlert.objects.get_or_create(
                    ip_address=ip_address,
                    user=user,
                    alert_type=LoginAlert.MULTIPLE_FAILURES,
                    defaults={
                        "severity": severity,
                        "description": f"{failed_attempts} failed attempts from {ip_address}",
                        "attempt_count": failed_attempts,
                    },
                )
                return True
        return False


class LoginAlert(models.Model):
    """Security alert system for authentication anomalies"""

    # Alert Types
    MULTIPLE_FAILURES = "multiple_failures"
    SUSPICIOUS_IP = "suspicious_ip"
    UNUSUAL_LOCATION = "unusual_location"
    BRUTE_FORCE = "brute_force"

    ALERT_TYPES = (
        (MULTIPLE_FAILURES, _("Multiple Failed Attempts")),
        (SUSPICIOUS_IP, _("Suspicious IP Address")),
        (UNUSUAL_LOCATION, _("Unusual Login Location")),
        (BRUTE_FORCE, _("Brute Force Attack")),
    )

    # Severity Levels
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

    SEVERITY_LEVELS = (
        (LOW, _("Low")),
        (MEDIUM, _("Medium")),
        (HIGH, _("High")),
        (CRITICAL, _("Critical")),
    )

    user = models.ForeignKey(
        "CustomUser",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="alerts",
        verbose_name=_("associated user"),
    )
    ip_address = models.GenericIPAddressField(_("source IP"))
    alert_type = models.CharField(_("type"), max_length=20, choices=ALERT_TYPES)
    severity = models.CharField(
        _("severity"), max_length=10, choices=SEVERITY_LEVELS, default=MEDIUM
    )
    description = models.TextField(_("details"))
    attempt_count = models.PositiveIntegerField(_("attempts"), default=1)
    is_resolved = models.BooleanField(_("resolved"), default=False)
    resolved_by = models.ForeignKey(
        "CustomUser",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="resolved_alerts",
        verbose_name=_("resolved by"),
    )
    resolved_at = models.DateTimeField(_("resolution time"), null=True, blank=True)
    resolution_notes = models.TextField(_("resolution notes"), blank=True)
    created = models.DateTimeField(_("created at"), auto_now_add=True)
    updated = models.DateTimeField(_("updated at"), auto_now=True)

    class Meta:
        verbose_name = _("Security Alert")
        verbose_name_plural = _("Security Alerts")
        ordering = ["-created"]
        unique_together = ["ip_address", "alert_type", "is_resolved"]

    def __str__(self):
        return f"{self.get_alert_type_display()} ({self.get_severity_display()})"

    def resolve(self, resolver, notes=""):
        """Mark alert as resolved with audit trail"""
        self.is_resolved = True
        self.resolved_by = resolver
        self.resolved_at = timezone.now()
        self.resolution_notes = notes
        self.save()

    @property
    def is_critical(self):
        """Check if alert requires immediate attention"""
        return self.severity in [self.HIGH, self.CRITICAL]

    @classmethod
    def unresolved_count(cls):
        """Count of pending alerts"""
        return cls.objects.filter(is_resolved=False).count()

    @classmethod
    def critical_count(cls):
        """Count of high-priority alerts"""
        return cls.objects.filter(
            is_resolved=False, severity__in=[cls.HIGH, cls.CRITICAL]
        ).count()


# --- Customer Profile ---


class CustomerProfile(models.Model):
    """Stores comprehensive customer profile information"""

    # Gender Constants
    MALE = "M"
    FEMALE = "F"
    OTHER = "O"

    GENDER_CHOICES = (
        (MALE, _("Male")),
        (FEMALE, _("Female")),
        (OTHER, _("Other")),
    )

    # Month Constants
    JANUARY = 1
    FEBRUARY = 2
    MARCH = 3
    APRIL = 4
    MAY = 5
    JUNE = 6
    JULY = 7
    AUGUST = 8
    SEPTEMBER = 9
    OCTOBER = 10
    NOVEMBER = 11
    DECEMBER = 12

    MONTH_CHOICES = (
        (JANUARY, _("January")),
        (FEBRUARY, _("February")),
        (MARCH, _("March")),
        (APRIL, _("April")),
        (MAY, _("May")),
        (JUNE, _("June")),
        (JULY, _("July")),
        (AUGUST, _("August")),
        (SEPTEMBER, _("September")),
        (OCTOBER, _("October")),
        (NOVEMBER, _("November")),
        (DECEMBER, _("December")),
    )

    user = models.OneToOneField(
        "CustomUser",
        on_delete=models.CASCADE,
        related_name="customer_profile",
        verbose_name=_("user account"),
    )
    first_name = models.CharField(_("first name"), max_length=100, blank=True)
    last_name = models.CharField(_("last name"), max_length=100, blank=True)
    profile_picture = models.ImageField(
        _("profile picture"),
        upload_to=get_customer_profile_image_path,
        blank=True,
        null=True,
        help_text=_("Profile image uploaded to cloud storage"),
    )
    gender = models.CharField(
        _("gender"), max_length=1, choices=GENDER_CHOICES, blank=True
    )
    birth_month = models.PositiveSmallIntegerField(
        _("birth month"), choices=MONTH_CHOICES, null=True, blank=True
    )
    birth_year = models.PositiveSmallIntegerField(
        _("birth year"), null=True, blank=True, help_text=_("Format: YYYY")
    )
    phone_number = models.CharField(
        _("phone number"),
        max_length=20,
        blank=True,
        validators=[
            RegexValidator(
                regex=r"^\+?1?\d{9,15}$",
                message=_("Enter a valid phone number (e.g., +**********)"),
            )
        ],
    )
    address = models.CharField(_("street address"), max_length=255, blank=True)
    city = models.CharField(_("city"), max_length=100, blank=True)
    zip_code = models.CharField(_("postal code"), max_length=10, blank=True)
    created_at = models.DateTimeField(_("created at"), auto_now_add=True)
    updated_at = models.DateTimeField(_("updated at"), auto_now=True)

    class Meta:
        verbose_name = _("Customer Profile")
        verbose_name_plural = _("Customer Profiles")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["last_name", "first_name"]),
            models.Index(fields=["city"]),
            models.Index(fields=["zip_code"]),
        ]

    def __str__(self):
        return _("%(email)s's profile") % {"email": self.user.email}

    @property
    def full_name(self) -> str:
        """Customer's full name combining first and last names"""
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def birth_month_name(self) -> str:
        """Get localized month name for birth month"""
        return dict(self.MONTH_CHOICES).get(self.birth_month, "")

    def get_absolute_url(self):
        return reverse("accounts_app:customer_profile")


# --- Service Provider Profile ---


class ServiceProviderProfile(models.Model):
    """Represents a service provider business with complete contact and operational details"""

    # US State Abbreviations
    ALABAMA = "AL"
    ALASKA = "AK"
    ARIZONA = "AZ"
    ARKANSAS = "AR"
    CALIFORNIA = "CA"
    COLORADO = "CO"
    CONNECTICUT = "CT"
    DELAWARE = "DE"
    FLORIDA = "FL"
    GEORGIA = "GA"
    HAWAII = "HI"
    IDAHO = "ID"
    ILLINOIS = "IL"
    INDIANA = "IN"
    IOWA = "IA"
    KANSAS = "KS"
    KENTUCKY = "KY"
    LOUISIANA = "LA"
    MAINE = "ME"
    MARYLAND = "MD"
    MASSACHUSETTS = "MA"
    MICHIGAN = "MI"
    MINNESOTA = "MN"
    MISSISSIPPI = "MS"
    MISSOURI = "MO"
    MONTANA = "MT"
    NEBRASKA = "NE"
    NEVADA = "NV"
    NEW_HAMPSHIRE = "NH"
    NEW_JERSEY = "NJ"
    NEW_MEXICO = "NM"
    NEW_YORK = "NY"
    NORTH_CAROLINA = "NC"
    NORTH_DAKOTA = "ND"
    OHIO = "OH"
    OKLAHOMA = "OK"
    OREGON = "OR"
    PENNSYLVANIA = "PA"
    RHODE_ISLAND = "RI"
    SOUTH_CAROLINA = "SC"
    SOUTH_DAKOTA = "SD"
    TENNESSEE = "TN"
    TEXAS = "TX"
    UTAH = "UT"
    VERMONT = "VT"
    VIRGINIA = "VA"
    WASHINGTON = "WA"
    WEST_VIRGINIA = "WV"
    WISCONSIN = "WI"
    WYOMING = "WY"

    STATE_CHOICES = (
        (ALABAMA, _("Alabama")),
        (ALASKA, _("Alaska")),
        (ARIZONA, _("Arizona")),
        (ARKANSAS, _("Arkansas")),
        (CALIFORNIA, _("California")),
        (COLORADO, _("Colorado")),
        (CONNECTICUT, _("Connecticut")),
        (DELAWARE, _("Delaware")),
        (FLORIDA, _("Florida")),
        (GEORGIA, _("Georgia")),
        (HAWAII, _("Hawaii")),
        (IDAHO, _("Idaho")),
        (ILLINOIS, _("Illinois")),
        (INDIANA, _("Indiana")),
        (IOWA, _("Iowa")),
        (KANSAS, _("Kansas")),
        (KENTUCKY, _("Kentucky")),
        (LOUISIANA, _("Louisiana")),
        (MAINE, _("Maine")),
        (MARYLAND, _("Maryland")),
        (MASSACHUSETTS, _("Massachusetts")),
        (MICHIGAN, _("Michigan")),
        (MINNESOTA, _("Minnesota")),
        (MISSISSIPPI, _("Mississippi")),
        (MISSOURI, _("Missouri")),
        (MONTANA, _("Montana")),
        (NEBRASKA, _("Nebraska")),
        (NEVADA, _("Nevada")),
        (NEW_HAMPSHIRE, _("New Hampshire")),
        (NEW_JERSEY, _("New Jersey")),
        (NEW_MEXICO, _("New Mexico")),
        (NEW_YORK, _("New York")),
        (NORTH_CAROLINA, _("North Carolina")),
        (NORTH_DAKOTA, _("North Dakota")),
        (OHIO, _("Ohio")),
        (OKLAHOMA, _("Oklahoma")),
        (OREGON, _("Oregon")),
        (PENNSYLVANIA, _("Pennsylvania")),
        (RHODE_ISLAND, _("Rhode Island")),
        (SOUTH_CAROLINA, _("South Carolina")),
        (SOUTH_DAKOTA, _("South Dakota")),
        (TENNESSEE, _("Tennessee")),
        (TEXAS, _("Texas")),
        (UTAH, _("Utah")),
        (VERMONT, _("Vermont")),
        (VIRGINIA, _("Virginia")),
        (WASHINGTON, _("Washington")),
        (WEST_VIRGINIA, _("West Virginia")),
        (WISCONSIN, _("Wisconsin")),
        (WYOMING, _("Wyoming")),
    )

    user = models.OneToOneField(
        "CustomUser",
        on_delete=models.CASCADE,
        related_name="service_provider_profile",
        verbose_name=_("business account"),
    )
    legal_name = models.CharField(
        _("legal business name"),
        max_length=200,
        help_text=_("Official registered business name"),
    )
    display_name = models.CharField(
        _("public display name"),
        max_length=200,
        blank=True,
        help_text=_("Name shown to customers (if different from legal name)"),
    )
    description = models.TextField(
        _("business description"),
        max_length=500,
        blank=True,
        help_text=_("Brief overview of services offered (500 characters max)"),
    )
    logo = models.ImageField(
        _("business logo"),
        upload_to=get_provider_profile_image_path,
        blank=True,
        null=True,
        help_text=_("Company logo displayed on your profile"),
    )
    phone = models.CharField(
        _("business phone"),
        max_length=20,
        validators=[
            RegexValidator(
                regex=r"^\+?1?\d{9,15}$",
                message=_("Enter a valid phone number (e.g., +**********)"),
            )
        ],
    )
    contact_name = models.CharField(
        _("primary contact"),
        max_length=100,
        help_text=_("Name of main business contact"),
    )
    address = models.CharField(_("street address"), max_length=255)
    city = models.CharField(_("city"), max_length=100)
    state = models.CharField(_("state"), max_length=2, choices=STATE_CHOICES)
    county = models.CharField(_("county"), max_length=100, blank=True)
    zip_code = models.CharField(_("ZIP code"), max_length=10)
    ein = models.CharField(
        _("EIN number"),
        max_length=20,
        blank=True,
        help_text=_("Employer Identification Number (optional)"),
    )
    website = models.URLField(_("website URL"), blank=True)
    instagram = models.URLField(_("Instagram URL"), blank=True)
    facebook = models.URLField(_("Facebook URL"), blank=True)
    is_public = models.BooleanField(
        _("public visibility"),
        default=True,
        help_text=_("Show business in public listings"),
    )

    # Tutorial and onboarding tracking
    venue_creation_tutorial_completed = models.BooleanField(
        _("venue creation tutorial completed"),
        default=False,
        help_text=_("Whether the user has completed the venue creation guided tour"),
    )

    created = models.DateTimeField(_("created at"), auto_now_add=True)
    updated = models.DateTimeField(_("updated at"), auto_now=True)

    class Meta:
        verbose_name = _("Service Provider")
        verbose_name_plural = _("Service Providers")
        ordering = ["-created"]
        indexes = [
            models.Index(fields=["legal_name"]),
            models.Index(fields=["city", "state"]),
            models.Index(fields=["is_public"]),
        ]

    def __str__(self):
        return f"{self.legal_name} ({self.user.email})"

    @property
    def business_name(self) -> str:
        """Name to display in UI (DBA name if available)"""
        return self.display_name or self.legal_name

    @property
    def full_address(self) -> str:
        """Formatted complete business address"""
        components = [self.address, self.city, f"{self.state} {self.zip_code}"]
        if self.county:
            components.insert(2, self.county)
        return ", ".join(filter(None, components))

    def get_absolute_url(self):
        return reverse("accounts_app:service_provider_profile")


# --- Team Member ---


class TeamMember(models.Model):
    """Represents a staff member associated with a service provider business"""

    service_provider = models.ForeignKey(
        "ServiceProviderProfile",
        on_delete=models.CASCADE,
        related_name="team",
        verbose_name=_("service provider"),
        help_text=_("Business this team member belongs to"),
    )
    name = models.CharField(
        _("full name"), max_length=100, help_text=_("Team member's full name")
    )
    position = models.CharField(
        _("position"), max_length=100, help_text=_("Role or job title")
    )
    photo = models.ImageField(
        _("profile photo"),
        upload_to=get_staff_profile_image_path,
        blank=True,
        null=True,
        help_text=_("Professional headshot (optional)"),
    )
    is_active = models.BooleanField(
        _("active status"),
        default=True,
        help_text=_("Is this team member currently active?"),
    )
    created = models.DateTimeField(_("created at"), auto_now_add=True)
    updated = models.DateTimeField(_("updated at"), auto_now=True)

    class Meta:
        verbose_name = _("Team Member")
        verbose_name_plural = _("Team Members")
        ordering = ["name"]
        constraints = [
            models.UniqueConstraint(
                fields=["service_provider", "name"], name="unique_team_member"
            )
        ]
        indexes = [
            models.Index(fields=["name"]),
            models.Index(fields=["position"]),
            models.Index(fields=["is_active"]),
            models.Index(fields=["service_provider", "is_active"]),
            models.Index(fields=["service_provider", "name"]),
        ]

    def __str__(self):
        return f"{self.name} ({self.position})"

    @classmethod
    def max_count(cls) -> int:
        """Maximum team members allowed per business (business rule)"""
        return 7
